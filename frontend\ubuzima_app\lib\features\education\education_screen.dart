import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/widgets/loading_overlay.dart';

/// Professional Education Screen
class EducationScreen extends ConsumerStatefulWidget {
  const EducationScreen({super.key});

  @override
  ConsumerState<EducationScreen> createState() => _EducationScreenState();
}

class _EducationScreenState extends ConsumerState<EducationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Health Education'),
        backgroundColor: AppColors.educationBlue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Featured'),
            Tab(text: 'Categories'),
            Tab(text: 'My Learning'),
            Tab(text: 'Search'),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildFeaturedTab(),
            _buildCategoriesTab(),
            _buildMyLearningTab(),
            _buildSearchTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Featured Lessons',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildFeaturedLesson(
            'Understanding Your Menstrual Cycle',
            'Learn about the phases of your cycle and what\'s normal',
            'Dr. Sarah Johnson',
            '15 min',
            4.8,
            'assets/images/cycle_lesson.jpg',
          ),
          const SizedBox(height: 12),
          _buildFeaturedLesson(
            'Contraception Options Explained',
            'Comprehensive guide to different birth control methods',
            'Dr. Michael Brown',
            '22 min',
            4.9,
            'assets/images/contraception_lesson.jpg',
          ),
          const SizedBox(height: 12),
          _buildFeaturedLesson(
            'Preparing for Pregnancy',
            'Essential steps to take before trying to conceive',
            'Dr. Emily Davis',
            '18 min',
            4.7,
            'assets/images/pregnancy_lesson.jpg',
          ),
          const SizedBox(height: 24),
          Text(
            'Popular This Week',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildPopularLessons(),
        ],
      ),
    );
  }

  Widget _buildFeaturedLesson(
    String title,
    String description,
    String instructor,
    String duration,
    double rating,
    String imagePath,
  ) {
    return Card(
      child: InkWell(
        onTap: () => _openLesson(title),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 160,
              decoration: BoxDecoration(
                color: AppColors.educationBlue.withOpacity(0.1),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Center(
                child: Icon(
                  Icons.play_circle_filled,
                  size: 64,
                  color: AppColors.educationBlue,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        instructor,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        duration,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(Icons.star, size: 16, color: AppColors.warning),
                      const SizedBox(width: 4),
                      Text(
                        rating.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularLessons() {
    final popularLessons = [
      'STI Prevention and Testing',
      'Fertility Awareness Methods',
      'Healthy Pregnancy Nutrition',
      'Postpartum Care Essentials',
    ];

    return Column(
      children:
          popularLessons.map((lesson) => _buildCompactLesson(lesson)).toList(),
    );
  }

  Widget _buildCompactLesson(String title) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.educationBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.play_arrow,
            color: AppColors.educationBlue,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Row(
          children: [
            Icon(Icons.access_time, size: 14, color: AppColors.textSecondary),
            const SizedBox(width: 4),
            Text(
              '12 min',
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
            const SizedBox(width: 16),
            Icon(Icons.star, size: 14, color: AppColors.warning),
            const SizedBox(width: 4),
            Text(
              '4.6',
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
          ],
        ),
        trailing: Icon(Icons.bookmark_border, color: AppColors.textSecondary),
        onTap: () => _openLesson(title),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Browse by Category',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.1,
            children: [
              _buildCategoryCard(
                'Family Planning',
                Icons.family_restroom,
                AppColors.primary,
                '24 lessons',
              ),
              _buildCategoryCard(
                'Reproductive Health',
                Icons.favorite,
                AppColors.error,
                '18 lessons',
              ),
              _buildCategoryCard(
                'Pregnancy & Birth',
                Icons.pregnant_woman,
                AppColors.pregnancyPurple,
                '32 lessons',
              ),
              _buildCategoryCard(
                'Contraception',
                Icons.shield,
                AppColors.contraceptionOrange,
                '15 lessons',
              ),
              _buildCategoryCard(
                'Sexual Health',
                Icons.health_and_safety,
                AppColors.secondary,
                '21 lessons',
              ),
              _buildCategoryCard(
                'Mental Wellness',
                Icons.psychology,
                AppColors.success,
                '16 lessons',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(
    String title,
    IconData icon,
    Color color,
    String lessonCount,
  ) {
    return Card(
      child: InkWell(
        onTap: () => _openCategory(title),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                lessonCount,
                style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMyLearningTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Learning Progress',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildProgressCard(),
          const SizedBox(height: 24),
          Text(
            'Continue Learning',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildInProgressLesson(
            'Understanding Your Menstrual Cycle',
            'Dr. Sarah Johnson',
            0.65,
            '10 of 15 min completed',
          ),
          const SizedBox(height: 12),
          _buildInProgressLesson(
            'Contraception Options Explained',
            'Dr. Michael Brown',
            0.30,
            '7 of 22 min completed',
          ),
          const SizedBox(height: 24),
          Text(
            'Completed Lessons',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildCompletedLesson('Fertility Awareness Methods', '4.8'),
          _buildCompletedLesson('STI Prevention and Testing', '4.9'),
          _buildCompletedLesson('Healthy Pregnancy Nutrition', '4.7'),
        ],
      ),
    );
  }

  Widget _buildProgressCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.educationBlue,
            AppColors.educationBlue.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.school, color: Colors.white, size: 24),
              const SizedBox(width: 8),
              Text(
                'Learning Stats',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatItem('Lessons Completed', '12')),
              Expanded(child: _buildStatItem('Hours Learned', '8.5')),
              Expanded(child: _buildStatItem('Certificates', '3')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.8)),
        ),
      ],
    );
  }

  Widget _buildInProgressLesson(
    String title,
    String instructor,
    double progress,
    String progressText,
  ) {
    return Card(
      child: InkWell(
        onTap: () => _openLesson(title),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.educationBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.play_arrow,
                      color: AppColors.educationBlue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        Text(
                          instructor,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.educationBlue,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                progressText,
                style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompletedLesson(String title, String rating) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.success.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.check_circle, color: AppColors.success, size: 20),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Row(
          children: [
            Icon(Icons.star, size: 14, color: AppColors.warning),
            const SizedBox(width: 4),
            Text(
              'Rated $rating',
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
          ],
        ),
        trailing: Icon(Icons.replay, color: AppColors.textSecondary),
        onTap: () => _openLesson(title),
      ),
    );
  }

  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search lessons, topics, instructors...',
              prefixIcon: Icon(Icons.search, color: AppColors.textSecondary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.educationBlue),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 16),
          if (_searchQuery.isEmpty) ...[
            _buildSearchSuggestions(),
          ] else ...[
            _buildSearchResults(),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Popular Searches',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                [
                  'Birth Control',
                  'Pregnancy',
                  'Fertility',
                  'STI Prevention',
                  'Menstrual Health',
                  'Family Planning',
                ].map((tag) => _buildSearchTag(tag)).toList(),
          ),
          const SizedBox(height: 24),
          Text(
            'Recent Searches',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildRecentSearch('Contraception methods'),
          _buildRecentSearch('Pregnancy nutrition'),
          _buildRecentSearch('Menstrual cycle tracking'),
        ],
      ),
    );
  }

  Widget _buildSearchTag(String tag) {
    return InkWell(
      onTap: () => _performSearch(tag),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.educationBlue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.educationBlue.withOpacity(0.3)),
        ),
        child: Text(
          tag,
          style: TextStyle(fontSize: 14, color: AppColors.educationBlue),
        ),
      ),
    );
  }

  Widget _buildRecentSearch(String search) {
    return ListTile(
      leading: Icon(Icons.history, color: AppColors.textSecondary),
      title: Text(
        search,
        style: TextStyle(fontSize: 16, color: AppColors.textPrimary),
      ),
      trailing: Icon(
        Icons.north_west,
        color: AppColors.textSecondary,
        size: 16,
      ),
      onTap: () => _performSearch(search),
    );
  }

  Widget _buildSearchResults() {
    // Mock search results
    final results =
        [
              'Understanding Your Menstrual Cycle',
              'Contraception Options Explained',
              'Preparing for Pregnancy',
            ]
            .where(
              (lesson) =>
                  lesson.toLowerCase().contains(_searchQuery.toLowerCase()),
            )
            .toList();

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${results.length} results for "$_searchQuery"',
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: results.length,
              itemBuilder: (context, index) {
                return _buildCompactLesson(results[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Action methods
  void _openLesson(String lessonTitle) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Opening lesson: $lessonTitle')));
  }

  void _openCategory(String categoryTitle) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Opening category: $categoryTitle')));
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
      _searchController.text = query;
    });
  }
}
