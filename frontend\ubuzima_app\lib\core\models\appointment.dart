/// Appointment model for the family planning platform
class Appointment {
  final int? id;
  final String title;
  final String? description;
  final DateTime appointmentDate;
  final String? appointmentTime;
  final String type;
  final String status;
  final String? doctorName;
  final String? facilityName;
  final String? facilityAddress;
  final String? notes;
  final String? reason;
  final bool isVirtual;
  final String? meetingLink;
  final DateTime? reminderTime;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Appointment({
    this.id,
    required this.title,
    this.description,
    required this.appointmentDate,
    this.appointmentTime,
    required this.type,
    this.status = 'scheduled',
    this.doctorName,
    this.facilityName,
    this.facilityAddress,
    this.notes,
    this.reason,
    this.isVirtual = false,
    this.meetingLink,
    this.reminderTime,
    this.createdAt,
    this.updatedAt,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'],
      appointmentDate: DateTime.parse(json['appointmentDate']),
      appointmentTime: json['appointmentTime'],
      type: json['type'] ?? 'consultation',
      status: json['status'] ?? 'scheduled',
      doctorName: json['doctorName'],
      facilityName: json['facilityName'],
      facilityAddress: json['facilityAddress'],
      notes: json['notes'],
      reason: json['reason'],
      isVirtual: json['isVirtual'] ?? false,
      meetingLink: json['meetingLink'],
      reminderTime: json['reminderTime'] != null 
          ? DateTime.parse(json['reminderTime']) 
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'appointmentDate': appointmentDate.toIso8601String(),
      'appointmentTime': appointmentTime,
      'type': type,
      'status': status,
      'doctorName': doctorName,
      'facilityName': facilityName,
      'facilityAddress': facilityAddress,
      'notes': notes,
      'reason': reason,
      'isVirtual': isVirtual,
      'meetingLink': meetingLink,
      'reminderTime': reminderTime?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Appointment copyWith({
    int? id,
    String? title,
    String? description,
    DateTime? appointmentDate,
    String? appointmentTime,
    String? type,
    String? status,
    String? doctorName,
    String? facilityName,
    String? facilityAddress,
    String? notes,
    String? reason,
    bool? isVirtual,
    String? meetingLink,
    DateTime? reminderTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Appointment(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      type: type ?? this.type,
      status: status ?? this.status,
      doctorName: doctorName ?? this.doctorName,
      facilityName: facilityName ?? this.facilityName,
      facilityAddress: facilityAddress ?? this.facilityAddress,
      notes: notes ?? this.notes,
      reason: reason ?? this.reason,
      isVirtual: isVirtual ?? this.isVirtual,
      meetingLink: meetingLink ?? this.meetingLink,
      reminderTime: reminderTime ?? this.reminderTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted appointment date
  String get formattedDate {
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }

  /// Get formatted appointment time
  String get formattedTime {
    if (appointmentTime != null) return appointmentTime!;
    
    final hour = appointmentDate.hour;
    final minute = appointmentDate.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get appointment type display name
  String get typeDisplayName {
    switch (type.toLowerCase()) {
      case 'consultation':
        return 'Consultation';
      case 'checkup':
        return 'Check-up';
      case 'followup':
        return 'Follow-up';
      case 'screening':
        return 'Screening';
      case 'vaccination':
        return 'Vaccination';
      case 'counseling':
        return 'Counseling';
      case 'emergency':
        return 'Emergency';
      default:
        return type;
    }
  }

  /// Get status display name
  String get statusDisplayName {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'Scheduled';
      case 'confirmed':
        return 'Confirmed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'no_show':
        return 'No Show';
      case 'rescheduled':
        return 'Rescheduled';
      default:
        return status;
    }
  }

  /// Get status color based on status
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return '#2196F3'; // Blue
      case 'confirmed':
        return '#4CAF50'; // Green
      case 'in_progress':
        return '#FF9800'; // Orange
      case 'completed':
        return '#4CAF50'; // Green
      case 'cancelled':
        return '#F44336'; // Red
      case 'no_show':
        return '#9E9E9E'; // Grey
      case 'rescheduled':
        return '#FF9800'; // Orange
      default:
        return '#2196F3'; // Blue
    }
  }

  /// Check if appointment is upcoming
  bool get isUpcoming {
    final now = DateTime.now();
    return appointmentDate.isAfter(now) && 
           (status == 'scheduled' || status == 'confirmed');
  }

  /// Check if appointment is today
  bool get isToday {
    final now = DateTime.now();
    return appointmentDate.year == now.year &&
           appointmentDate.month == now.month &&
           appointmentDate.day == now.day;
  }

  /// Check if appointment is overdue
  bool get isOverdue {
    final now = DateTime.now();
    return appointmentDate.isBefore(now) && 
           (status == 'scheduled' || status == 'confirmed');
  }

  /// Get time until appointment
  Duration? get timeUntilAppointment {
    if (!isUpcoming) return null;
    final now = DateTime.now();
    return appointmentDate.difference(now);
  }

  /// Get days until appointment
  int? get daysUntilAppointment {
    final duration = timeUntilAppointment;
    if (duration == null) return null;
    return duration.inDays;
  }

  /// Get hours until appointment
  int? get hoursUntilAppointment {
    final duration = timeUntilAppointment;
    if (duration == null) return null;
    return duration.inHours;
  }

  /// Check if reminder should be shown
  bool get shouldShowReminder {
    if (reminderTime == null) return false;
    final now = DateTime.now();
    return now.isAfter(reminderTime!) && isUpcoming;
  }

  /// Get appointment location
  String get location {
    if (isVirtual) return 'Virtual Meeting';
    if (facilityName != null) {
      if (facilityAddress != null) {
        return '$facilityName, $facilityAddress';
      }
      return facilityName!;
    }
    return 'Location not specified';
  }

  /// Check if appointment can be cancelled
  bool get canBeCancelled {
    return status == 'scheduled' || status == 'confirmed';
  }

  /// Check if appointment can be rescheduled
  bool get canBeRescheduled {
    return status == 'scheduled' || status == 'confirmed';
  }

  @override
  String toString() {
    return 'Appointment{id: $id, title: $title, date: $formattedDate, status: $status}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Appointment &&
           other.id == id &&
           other.title == title &&
           other.appointmentDate == appointmentDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ appointmentDate.hashCode;
  }
}
