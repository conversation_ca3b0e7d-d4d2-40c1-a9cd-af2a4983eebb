import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/widgets/loading_overlay.dart';

/// Professional Contraception Management Screen
class ContraceptionScreen extends ConsumerStatefulWidget {
  const ContraceptionScreen({super.key});

  @override
  ConsumerState<ContraceptionScreen> createState() =>
      _ContraceptionScreenState();
}

class _ContraceptionScreenState extends ConsumerState<ContraceptionScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Contraception'),
        backgroundColor: AppColors.contraceptionOrange,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Methods'),
            Tab(text: 'My Methods'),
            Tab(text: 'Education'),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildMethodsTab(),
            _buildMyMethodsTab(),
            _buildEducationTab(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddMethodDialog,
        backgroundColor: AppColors.contraceptionOrange,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildMethodsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Contraception Methods',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildMethodCategory('Hormonal Methods', [
            _buildMethodInfo(
              'Birth Control Pills',
              'Daily oral contraceptive',
              '99%',
              Icons.medication,
            ),
            _buildMethodInfo(
              'IUD (Hormonal)',
              'Long-term intrauterine device',
              '99%',
              Icons.circle,
            ),
            _buildMethodInfo(
              'Implant',
              'Arm implant (3 years)',
              '99%',
              Icons.linear_scale,
            ),
            _buildMethodInfo(
              'Injection',
              'Quarterly injection',
              '96%',
              Icons.vaccines,
            ),
          ]),
          const SizedBox(height: 16),
          _buildMethodCategory('Barrier Methods', [
            _buildMethodInfo(
              'Condoms',
              'Male/female condoms',
              '85%',
              Icons.shield,
            ),
            _buildMethodInfo(
              'Diaphragm',
              'Cervical barrier',
              '88%',
              Icons.circle_outlined,
            ),
            _buildMethodInfo(
              'Cervical Cap',
              'Small silicone cap',
              '84%',
              Icons.circle,
            ),
          ]),
          const SizedBox(height: 16),
          _buildMethodCategory('Natural Methods', [
            _buildMethodInfo(
              'Fertility Awareness',
              'Cycle tracking method',
              '76%',
              Icons.calendar_month,
            ),
            _buildMethodInfo(
              'Withdrawal',
              'Pull-out method',
              '78%',
              Icons.back_hand,
            ),
          ]),
          const SizedBox(height: 16),
          _buildMethodCategory('Permanent Methods', [
            _buildMethodInfo(
              'Tubal Ligation',
              'Female sterilization',
              '99%',
              Icons.block,
            ),
            _buildMethodInfo(
              'Vasectomy',
              'Male sterilization',
              '99%',
              Icons.block,
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildMethodCategory(String title, List<Widget> methods) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ...methods,
      ],
    );
  }

  Widget _buildMethodInfo(
    String name,
    String description,
    String effectiveness,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _showMethodDetails(name, description, effectiveness),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.contraceptionOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppColors.contraceptionOrange,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      effectiveness,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Effective',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMyMethodsTab() {
    // Mock data for demonstration
    final myMethods = [
      {
        'name': 'Birth Control Pills',
        'startDate': '2024-01-15',
        'status': 'Active',
        'effectiveness': '99%',
        'nextAction': 'Take daily at 8 PM',
      },
    ];

    if (myMethods.isEmpty) {
      return _buildEmptyState(
        'No methods tracked',
        'Add your contraception methods to track them',
        Icons.shield,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: myMethods.length,
      itemBuilder: (context, index) {
        final method = myMethods[index];
        return _buildMyMethodCard(method);
      },
    );
  }

  Widget _buildMyMethodCard(Map<String, String> method) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.contraceptionOrange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.medication,
                    color: AppColors.contraceptionOrange,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method['name']!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Started: ${method['startDate']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    method['status']!,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.success,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.secondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.alarm, color: AppColors.secondary, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      method['nextAction']!,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.secondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _editMethod(method),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _stopMethod(method),
                    icon: const Icon(Icons.stop, size: 16),
                    label: const Text('Stop'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.error,
                      side: BorderSide(color: AppColors.error),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contraception Education',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildEducationSection('Getting Started', [
            'Choosing the Right Method',
            'Effectiveness Comparison',
            'Side Effects Guide',
            'Cost Considerations',
          ]),
          const SizedBox(height: 16),
          _buildEducationSection('Method-Specific Guides', [
            'How to Take Birth Control Pills',
            'IUD Insertion and Care',
            'Condom Usage Guide',
            'Natural Family Planning',
          ]),
          const SizedBox(height: 16),
          _buildEducationSection('Health & Safety', [
            'When to See a Doctor',
            'Emergency Contraception',
            'STI Prevention',
            'Pregnancy Planning',
          ]),
        ],
      ),
    );
  }

  Widget _buildEducationSection(String title, List<String> topics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ...topics.map((topic) => _buildEducationItem(topic)).toList(),
      ],
    );
  }

  Widget _buildEducationItem(String title) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.contraceptionOrange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.school,
            color: AppColors.contraceptionOrange,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textSecondary,
        ),
        onTap: () => _openEducationTopic(title),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Action methods
  void _showAddMethodDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Contraception Method'),
            content: const Text('Choose a method to start tracking'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _addNewMethod();
                },
                child: const Text('Add Method'),
              ),
            ],
          ),
    );
  }

  void _addNewMethod() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('New contraception method added!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showMethodDetails(
    String name,
    String description,
    String effectiveness,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(name),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(description),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Text(
                      'Effectiveness: ',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(effectiveness),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _addMethodToTracking(name);
                },
                child: const Text('Start Tracking'),
              ),
            ],
          ),
    );
  }

  void _addMethodToTracking(String methodName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$methodName added to your tracking!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _editMethod(Map<String, String> method) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Edit ${method['name']}'),
            content: const Text('Edit method details - Coming Soon'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Method updated successfully'),
                    ),
                  );
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _stopMethod(Map<String, String> method) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Stop ${method['name']}?'),
            content: const Text(
              'Are you sure you want to stop tracking this method?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${method['name']} stopped successfully'),
                      backgroundColor: AppColors.warning,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: AppColors.error),
                child: const Text('Stop'),
              ),
            ],
          ),
    );
  }

  void _openEducationTopic(String topic) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Opening $topic')));
  }
}
