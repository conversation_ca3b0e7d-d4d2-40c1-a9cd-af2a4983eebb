/// Health Record model for the family planning platform
class HealthRecord {
  final int? id;
  final String title;
  final String recordType;
  final DateTime recordDate;
  final String? description;
  final String? diagnosis;
  final String? treatment;
  final String? notes;
  final String? doctorName;
  final String? facilityName;
  final List<String>? attachments;
  final Map<String, dynamic>? vitals;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  HealthRecord({
    this.id,
    required this.title,
    required this.recordType,
    required this.recordDate,
    this.description,
    this.diagnosis,
    this.treatment,
    this.notes,
    this.doctorName,
    this.facilityName,
    this.attachments,
    this.vitals,
    this.createdAt,
    this.updatedAt,
  });

  factory HealthRecord.fromJson(Map<String, dynamic> json) {
    return HealthRecord(
      id: json['id'],
      title: json['title'] ?? '',
      recordType: json['recordType'] ?? 'General',
      recordDate: json['recordDate'] != null 
          ? DateTime.parse(json['recordDate']) 
          : DateTime.now(),
      description: json['description'],
      diagnosis: json['diagnosis'],
      treatment: json['treatment'],
      notes: json['notes'],
      doctorName: json['doctorName'],
      facilityName: json['facilityName'],
      attachments: json['attachments'] != null 
          ? List<String>.from(json['attachments'])
          : null,
      vitals: json['vitals'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'recordType': recordType,
      'recordDate': recordDate.toIso8601String(),
      'description': description,
      'diagnosis': diagnosis,
      'treatment': treatment,
      'notes': notes,
      'doctorName': doctorName,
      'facilityName': facilityName,
      'attachments': attachments,
      'vitals': vitals,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  HealthRecord copyWith({
    int? id,
    String? title,
    String? recordType,
    DateTime? recordDate,
    String? description,
    String? diagnosis,
    String? treatment,
    String? notes,
    String? doctorName,
    String? facilityName,
    List<String>? attachments,
    Map<String, dynamic>? vitals,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HealthRecord(
      id: id ?? this.id,
      title: title ?? this.title,
      recordType: recordType ?? this.recordType,
      recordDate: recordDate ?? this.recordDate,
      description: description ?? this.description,
      diagnosis: diagnosis ?? this.diagnosis,
      treatment: treatment ?? this.treatment,
      notes: notes ?? this.notes,
      doctorName: doctorName ?? this.doctorName,
      facilityName: facilityName ?? this.facilityName,
      attachments: attachments ?? this.attachments,
      vitals: vitals ?? this.vitals,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted record date
  String get formattedDate {
    return '${recordDate.day}/${recordDate.month}/${recordDate.year}';
  }

  /// Get record type display name
  String get recordTypeDisplayName {
    switch (recordType.toLowerCase()) {
      case 'general':
        return 'General Checkup';
      case 'laboratory':
        return 'Lab Results';
      case 'consultation':
        return 'Consultation';
      case 'prescription':
        return 'Prescription';
      case 'vaccination':
        return 'Vaccination';
      case 'emergency':
        return 'Emergency Visit';
      default:
        return recordType;
    }
  }

  /// Check if record has attachments
  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;

  /// Check if record has vitals
  bool get hasVitals => vitals != null && vitals!.isNotEmpty;

  /// Get vital signs as formatted string
  String get vitalsString {
    if (!hasVitals) return 'No vitals recorded';
    
    final List<String> vitalsList = [];
    vitals!.forEach((key, value) {
      vitalsList.add('$key: $value');
    });
    
    return vitalsList.join(', ');
  }

  /// Check if record is recent (within last 30 days)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(recordDate);
    return difference.inDays <= 30;
  }

  /// Get record priority based on type
  int get priority {
    switch (recordType.toLowerCase()) {
      case 'emergency':
        return 1;
      case 'laboratory':
        return 2;
      case 'consultation':
        return 3;
      case 'prescription':
        return 4;
      case 'vaccination':
        return 5;
      case 'general':
      default:
        return 6;
    }
  }

  @override
  String toString() {
    return 'HealthRecord{id: $id, title: $title, recordType: $recordType, recordDate: $recordDate}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HealthRecord &&
           other.id == id &&
           other.title == title &&
           other.recordType == recordType &&
           other.recordDate == recordDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^
           title.hashCode ^
           recordType.hashCode ^
           recordDate.hashCode;
  }
}
