import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/widgets/loading_overlay.dart';
import '../../core/providers/health_provider.dart';
import '../../core/models/health_record.dart';

/// Professional Health Records Screen with Real API Integration
class HealthRecordsScreen extends ConsumerStatefulWidget {
  const HealthRecordsScreen({super.key});

  @override
  ConsumerState<HealthRecordsScreen> createState() =>
      _HealthRecordsScreenState();
}

class _HealthRecordsScreenState extends ConsumerState<HealthRecordsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Load health records when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(healthProvider.notifier).loadHealthRecords();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _addHealthRecord() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddHealthRecordScreen()),
    );

    if (result == true) {
      ref.read(healthProvider.notifier).loadHealthRecords();
    }
  }

  @override
  Widget build(BuildContext context) {
    final healthState = ref.watch(healthProvider);
    final healthRecords = ref.watch(healthRecordsProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Health Records'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All Records'),
            Tab(text: 'Recent'),
            Tab(text: 'Reports'),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: healthState.isLoading,
        child:
            healthState.error != null
                ? _buildErrorState(healthState.error!)
                : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllRecordsTab(healthRecords),
                    _buildRecentRecordsTab(healthRecords),
                    _buildReportsTab(),
                  ],
                ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addHealthRecord,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAllRecordsTab(List<HealthRecord> healthRecords) {
    if (healthRecords.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(healthProvider.notifier).loadHealthRecords(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: healthRecords.length,
        itemBuilder: (context, index) {
          final record = healthRecords[index];
          return _buildHealthRecordCard(record);
        },
      ),
    );
  }

  Widget _buildRecentRecordsTab(List<HealthRecord> healthRecords) {
    final recentRecords =
        healthRecords
            .where(
              (record) =>
                  DateTime.now().difference(record.recordDate).inDays <= 30,
            )
            .toList();

    if (recentRecords.isEmpty) {
      return _buildEmptyState(message: 'No recent health records');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: recentRecords.length,
      itemBuilder: (context, index) {
        final record = recentRecords[index];
        return _buildHealthRecordCard(record);
      },
    );
  }

  Widget _buildReportsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'Health Reports',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Detailed health analytics coming soon',
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthRecordCard(HealthRecord record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _viewHealthRecord(record),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getRecordTypeColor(
                        record.recordType,
                      ).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getRecordTypeIcon(record.recordType),
                      color: _getRecordTypeColor(record.recordType),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          record.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          record.recordType,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _editHealthRecord(record),
                    icon: Icon(
                      Icons.edit_outlined,
                      size: 20,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _deleteHealthRecord(record),
                    icon: Icon(
                      Icons.delete_outline,
                      size: 20,
                      color: AppColors.error,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (record.description != null)
                Text(
                  record.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(record.recordDate),
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_outlined, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            message ?? 'No health records yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to add your first health record',
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            'Error Loading Records',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ref.read(healthProvider.notifier).clearError();
              ref.read(healthProvider.notifier).loadHealthRecords();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _viewHealthRecord(HealthRecord record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ViewHealthRecordScreen(record: record),
      ),
    );
  }

  void _editHealthRecord(HealthRecord record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditHealthRecordScreen(record: record),
      ),
    ).then((result) {
      if (result == true) {
        ref.read(healthProvider.notifier).loadHealthRecords();
      }
    });
  }

  void _deleteHealthRecord(HealthRecord record) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Health Record'),
            content: Text('Are you sure you want to delete "${record.title}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performDelete(record);
                },
                style: TextButton.styleFrom(foregroundColor: AppColors.error),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _performDelete(HealthRecord record) async {
    if (record.id != null) {
      final success = await ref
          .read(healthProvider.notifier)
          .deleteHealthRecord(record.id!);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Health record deleted successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  Color _getRecordTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'general':
        return AppColors.primary;
      case 'laboratory':
        return AppColors.tertiary;
      case 'consultation':
        return AppColors.secondary;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getRecordTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'general':
        return Icons.medical_services;
      case 'laboratory':
        return Icons.science;
      case 'consultation':
        return Icons.chat;
      default:
        return Icons.description;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Add Health Record Screen - FULLY IMPLEMENTED
class AddHealthRecordScreen extends ConsumerStatefulWidget {
  const AddHealthRecordScreen({super.key});

  @override
  ConsumerState<AddHealthRecordScreen> createState() =>
      _AddHealthRecordScreenState();
}

class _AddHealthRecordScreenState extends ConsumerState<AddHealthRecordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _heartRateController = TextEditingController();
  final _bloodPressureController = TextEditingController();
  final _weightController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedType = 'general';
  bool _isLoading = false;

  @override
  void dispose() {
    _heartRateController.dispose();
    _bloodPressureController.dispose();
    _weightController.dispose();
    _temperatureController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveHealthRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Build vitals map
      final vitalsMap = <String, dynamic>{};
      if (_heartRateController.text.isNotEmpty) {
        vitalsMap['heartRate'] = int.tryParse(_heartRateController.text);
      }
      if (_bloodPressureController.text.isNotEmpty) {
        vitalsMap['bloodPressure'] = _bloodPressureController.text;
      }
      if (_weightController.text.isNotEmpty) {
        vitalsMap['weight'] = double.tryParse(_weightController.text);
      }
      if (_temperatureController.text.isNotEmpty) {
        vitalsMap['temperature'] = double.tryParse(_temperatureController.text);
      }
      vitalsMap['healthStatus'] = _calculateHealthStatus();

      final healthRecord = HealthRecord(
        title:
            '${_selectedType.toUpperCase()} - ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
        recordType: _selectedType,
        recordDate: DateTime.now(),
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        vitals: vitalsMap.isNotEmpty ? vitalsMap : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await ref
          .read(healthProvider.notifier)
          .createHealthRecord(healthRecord);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Health record saved successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save health record: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  String _calculateHealthStatus() {
    // Simple health status calculation based on vital signs
    final heartRate = int.tryParse(_heartRateController.text) ?? 0;
    final temperature = double.tryParse(_temperatureController.text) ?? 0;

    if (heartRate > 100 || temperature > 37.5) {
      return 'urarwaye'; // Critical/sick
    } else if (heartRate > 90 || temperature > 37.0) {
      return 'iyiteho'; // Take care
    } else {
      return 'mwiza'; // Normal/good
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Health Record'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Record Type Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Record Type',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedType,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'general',
                          child: Text('General Checkup'),
                        ),
                        DropdownMenuItem(
                          value: 'consultation',
                          child: Text('Consultation'),
                        ),
                        DropdownMenuItem(
                          value: 'laboratory',
                          child: Text('Laboratory Results'),
                        ),
                        DropdownMenuItem(
                          value: 'emergency',
                          child: Text('Emergency Visit'),
                        ),
                      ],
                      onChanged:
                          (value) => setState(() => _selectedType = value!),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Vital Signs
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Vital Signs',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Heart Rate
                    TextFormField(
                      controller: _heartRateController,
                      decoration: const InputDecoration(
                        labelText: 'Heart Rate (bpm)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.favorite, color: Colors.red),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final rate = int.tryParse(value);
                          if (rate == null || rate < 30 || rate > 200) {
                            return 'Enter a valid heart rate (30-200 bpm)';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Blood Pressure
                    TextFormField(
                      controller: _bloodPressureController,
                      decoration: const InputDecoration(
                        labelText: 'Blood Pressure (e.g., 120/80)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.monitor_heart,
                          color: Colors.blue,
                        ),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (!RegExp(r'^\d{2,3}\/\d{2,3}$').hasMatch(value)) {
                            return 'Enter in format: 120/80';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Weight
                    TextFormField(
                      controller: _weightController,
                      decoration: const InputDecoration(
                        labelText: 'Weight (kg)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.monitor_weight,
                          color: Colors.green,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final weight = double.tryParse(value);
                          if (weight == null || weight < 20 || weight > 300) {
                            return 'Enter a valid weight (20-300 kg)';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Temperature
                    TextFormField(
                      controller: _temperatureController,
                      decoration: const InputDecoration(
                        labelText: 'Temperature (°C)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.thermostat,
                          color: Colors.orange,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final temp = double.tryParse(value);
                          if (temp == null || temp < 30 || temp > 45) {
                            return 'Enter a valid temperature (30-45°C)';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Notes
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Notes',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText: 'Enter any additional notes or symptoms...',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 4,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Save Button
            ElevatedButton(
              onPressed: _isLoading ? null : _saveHealthRecord,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child:
                  _isLoading
                      ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Text(
                        'Save Health Record',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVitalsGrid(Map<String, dynamic> vitals) {
    final vitalsList = <Widget>[];

    vitals.forEach((key, value) {
      if (value != null) {
        vitalsList.add(_buildVitalCard(key, value.toString()));
      }
    });

    return Wrap(spacing: 12, runSpacing: 12, children: vitalsList);
  }

  Widget _buildVitalCard(String label, String value) {
    IconData icon;
    Color color;
    String unit = '';

    switch (label.toLowerCase()) {
      case 'heartrate':
        icon = Icons.favorite;
        color = Colors.red;
        unit = ' bpm';
        label = 'Heart Rate';
        break;
      case 'bloodpressure':
        icon = Icons.monitor_heart;
        color = Colors.blue;
        label = 'Blood Pressure';
        break;
      case 'weight':
        icon = Icons.monitor_weight;
        color = Colors.green;
        unit = ' kg';
        label = 'Weight';
        break;
      case 'temperature':
        icon = Icons.thermostat;
        color = Colors.orange;
        unit = '°C';
        label = 'Temperature';
        break;
      case 'healthstatus':
        icon = Icons.health_and_safety;
        color = _getHealthStatusColor(value);
        label = 'Health Status';
        value = _getHealthStatusText(value);
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '$value$unit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Color _getHealthStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'mwiza':
        return AppColors.success;
      case 'iyiteho':
        return Colors.orange;
      case 'urarwaye':
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  String _getHealthStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'mwiza':
        return 'Mwiza (Good)';
      case 'iyiteho':
        return 'Iyiteho (Take Care)';
      case 'urarwaye':
        return 'Urarwaye (Critical)';
      default:
        return status;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _shareRecord(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }

  void _downloadAttachment(BuildContext context, String attachment) {
    // TODO: Implement download functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading $attachment...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}

class ViewHealthRecordScreen extends StatelessWidget {
  final HealthRecord record;

  const ViewHealthRecordScreen({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(record.title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EditHealthRecordScreen(record: record),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareRecord(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _getRecordTypeColor(
                              record.recordType,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getRecordTypeIcon(record.recordType),
                            color: _getRecordTypeColor(record.recordType),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                record.recordTypeDisplayName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                record.formattedDate,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (record.isRecent)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Recent',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Vital Signs Card
            if (record.hasVitals) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.monitor_heart, color: AppColors.primary),
                          SizedBox(width: 8),
                          Text(
                            'Vital Signs',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildVitalsGrid(record.vitals!),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Description Card
            if (record.description != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.description, color: AppColors.primary),
                          SizedBox(width: 8),
                          Text(
                            'Description',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        record.description!,
                        style: const TextStyle(fontSize: 16, height: 1.5),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Diagnosis Card
            if (record.diagnosis != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(
                            Icons.medical_information,
                            color: AppColors.error,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Diagnosis',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.error.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          record.diagnosis!,
                          style: const TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Treatment Card
            if (record.treatment != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.healing, color: AppColors.success),
                          SizedBox(width: 8),
                          Text(
                            'Treatment',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.success.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          record.treatment!,
                          style: const TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Notes Card
            if (record.notes != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.note, color: AppColors.secondary),
                          SizedBox(width: 8),
                          Text(
                            'Additional Notes',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        record.notes!,
                        style: const TextStyle(fontSize: 16, height: 1.5),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Healthcare Provider Info
            if (record.doctorName != null || record.facilityName != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.local_hospital, color: AppColors.tertiary),
                          SizedBox(width: 8),
                          Text(
                            'Healthcare Provider',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      if (record.doctorName != null) ...[
                        Row(
                          children: [
                            const Icon(
                              Icons.person,
                              size: 20,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Doctor: ${record.doctorName}',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                      ],
                      if (record.facilityName != null) ...[
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              size: 20,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Facility: ${record.facilityName}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Attachments Card
            if (record.hasAttachments) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.attach_file, color: AppColors.primary),
                          SizedBox(width: 8),
                          Text(
                            'Attachments',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      ...record.attachments!.map((attachment) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.insert_drive_file,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  attachment,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.download),
                                onPressed:
                                    () => _downloadAttachment(
                                      context,
                                      attachment,
                                    ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Metadata Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.info, color: Colors.grey),
                        SizedBox(width: 8),
                        Text(
                          'Record Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Record ID', record.id?.toString() ?? 'N/A'),
                    _buildInfoRow('Record Type', record.recordTypeDisplayName),
                    _buildInfoRow('Date Created', record.formattedDate),
                    if (record.createdAt != null)
                      _buildInfoRow(
                        'Created At',
                        _formatDateTime(record.createdAt!),
                      ),
                    if (record.updatedAt != null)
                      _buildInfoRow(
                        'Last Updated',
                        _formatDateTime(record.updatedAt!),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVitalsGrid(Map<String, dynamic> vitals) {
    final vitalsList = <Widget>[];

    vitals.forEach((key, value) {
      if (value != null) {
        vitalsList.add(_buildVitalCard(key, value.toString()));
      }
    });

    return Wrap(spacing: 12, runSpacing: 12, children: vitalsList);
  }

  Widget _buildVitalCard(String label, String value) {
    IconData icon;
    Color color;
    String unit = '';

    switch (label.toLowerCase()) {
      case 'heartrate':
        icon = Icons.favorite;
        color = Colors.red;
        unit = ' bpm';
        label = 'Heart Rate';
        break;
      case 'bloodpressure':
        icon = Icons.monitor_heart;
        color = Colors.blue;
        label = 'Blood Pressure';
        break;
      case 'weight':
        icon = Icons.monitor_weight;
        color = Colors.green;
        unit = ' kg';
        label = 'Weight';
        break;
      case 'temperature':
        icon = Icons.thermostat;
        color = Colors.orange;
        unit = '°C';
        label = 'Temperature';
        break;
      case 'healthstatus':
        icon = Icons.health_and_safety;
        color = _getHealthStatusColor(value);
        label = 'Health Status';
        value = _getHealthStatusText(value);
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '$value$unit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Color _getRecordTypeColor(String recordType) {
    switch (recordType.toLowerCase()) {
      case 'emergency':
        return AppColors.error;
      case 'laboratory':
        return AppColors.tertiary;
      case 'consultation':
        return AppColors.secondary;
      case 'prescription':
        return AppColors.medicationPink;
      case 'vaccination':
        return AppColors.success;
      case 'general':
      default:
        return AppColors.primary;
    }
  }

  IconData _getRecordTypeIcon(String recordType) {
    switch (recordType.toLowerCase()) {
      case 'emergency':
        return Icons.emergency;
      case 'laboratory':
        return Icons.science;
      case 'consultation':
        return Icons.medical_services;
      case 'prescription':
        return Icons.medication;
      case 'vaccination':
        return Icons.vaccines;
      case 'general':
      default:
        return Icons.health_and_safety;
    }
  }

  Color _getHealthStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'mwiza':
        return AppColors.success;
      case 'iyiteho':
        return Colors.orange;
      case 'urarwaye':
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  String _getHealthStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'mwiza':
        return 'Mwiza (Good)';
      case 'iyiteho':
        return 'Iyiteho (Take Care)';
      case 'urarwaye':
        return 'Urarwaye (Critical)';
      default:
        return status;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _shareRecord(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }

  void _downloadAttachment(BuildContext context, String attachment) {
    // TODO: Implement download functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading $attachment...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}

class EditHealthRecordScreen extends ConsumerStatefulWidget {
  final HealthRecord record;

  const EditHealthRecordScreen({super.key, required this.record});

  @override
  ConsumerState<EditHealthRecordScreen> createState() =>
      _EditHealthRecordScreenState();
}

class _EditHealthRecordScreenState
    extends ConsumerState<EditHealthRecordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _diagnosisController = TextEditingController();
  final _treatmentController = TextEditingController();
  final _notesController = TextEditingController();
  final _doctorNameController = TextEditingController();
  final _facilityNameController = TextEditingController();

  // Vitals controllers
  final _heartRateController = TextEditingController();
  final _bloodPressureController = TextEditingController();
  final _weightController = TextEditingController();
  final _temperatureController = TextEditingController();

  String _selectedType = 'general';
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _titleController.text = widget.record.title;
    _descriptionController.text = widget.record.description ?? '';
    _diagnosisController.text = widget.record.diagnosis ?? '';
    _treatmentController.text = widget.record.treatment ?? '';
    _notesController.text = widget.record.notes ?? '';
    _doctorNameController.text = widget.record.doctorName ?? '';
    _facilityNameController.text = widget.record.facilityName ?? '';
    _selectedType = widget.record.recordType;
    _selectedDate = widget.record.recordDate;

    // Initialize vitals if available
    if (widget.record.hasVitals) {
      final vitals = widget.record.vitals!;
      _heartRateController.text = vitals['heartRate']?.toString() ?? '';
      _bloodPressureController.text = vitals['bloodPressure']?.toString() ?? '';
      _weightController.text = vitals['weight']?.toString() ?? '';
      _temperatureController.text = vitals['temperature']?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _diagnosisController.dispose();
    _treatmentController.dispose();
    _notesController.dispose();
    _doctorNameController.dispose();
    _facilityNameController.dispose();
    _heartRateController.dispose();
    _bloodPressureController.dispose();
    _weightController.dispose();
    _temperatureController.dispose();
    super.dispose();
  }

  Future<void> _updateHealthRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Build vitals map
      final vitalsMap = <String, dynamic>{};
      if (_heartRateController.text.isNotEmpty) {
        vitalsMap['heartRate'] = int.tryParse(_heartRateController.text);
      }
      if (_bloodPressureController.text.isNotEmpty) {
        vitalsMap['bloodPressure'] = _bloodPressureController.text;
      }
      if (_weightController.text.isNotEmpty) {
        vitalsMap['weight'] = double.tryParse(_weightController.text);
      }
      if (_temperatureController.text.isNotEmpty) {
        vitalsMap['temperature'] = double.tryParse(_temperatureController.text);
      }
      vitalsMap['healthStatus'] = _calculateHealthStatus();

      final updatedRecord = widget.record.copyWith(
        title: _titleController.text,
        recordType: _selectedType,
        recordDate: _selectedDate,
        description:
            _descriptionController.text.isNotEmpty
                ? _descriptionController.text
                : null,
        diagnosis:
            _diagnosisController.text.isNotEmpty
                ? _diagnosisController.text
                : null,
        treatment:
            _treatmentController.text.isNotEmpty
                ? _treatmentController.text
                : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        doctorName:
            _doctorNameController.text.isNotEmpty
                ? _doctorNameController.text
                : null,
        facilityName:
            _facilityNameController.text.isNotEmpty
                ? _facilityNameController.text
                : null,
        vitals: vitalsMap.isNotEmpty ? vitalsMap : null,
        updatedAt: DateTime.now(),
      );

      final success = await ref
          .read(healthProvider.notifier)
          .updateHealthRecord(updatedRecord);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Health record updated successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update health record: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  String _calculateHealthStatus() {
    final heartRate = int.tryParse(_heartRateController.text) ?? 0;
    final temperature = double.tryParse(_temperatureController.text) ?? 0;

    if (heartRate > 100 || temperature > 37.5) {
      return 'urarwaye'; // Critical/sick
    } else if (heartRate > 90 || temperature > 37.0) {
      return 'iyiteho'; // Take care
    } else {
      return 'mwiza'; // Normal/good
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Health Record'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Basic Information Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Basic Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Title
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Record Title',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.title),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a title';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Record Type
                    DropdownButtonFormField<String>(
                      value: _selectedType,
                      decoration: const InputDecoration(
                        labelText: 'Record Type',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'general',
                          child: Text('General Checkup'),
                        ),
                        DropdownMenuItem(
                          value: 'consultation',
                          child: Text('Consultation'),
                        ),
                        DropdownMenuItem(
                          value: 'laboratory',
                          child: Text('Laboratory Results'),
                        ),
                        DropdownMenuItem(
                          value: 'emergency',
                          child: Text('Emergency Visit'),
                        ),
                        DropdownMenuItem(
                          value: 'prescription',
                          child: Text('Prescription'),
                        ),
                        DropdownMenuItem(
                          value: 'vaccination',
                          child: Text('Vaccination'),
                        ),
                      ],
                      onChanged:
                          (value) => setState(() => _selectedType = value!),
                    ),
                    const SizedBox(height: 16),

                    // Date
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() => _selectedDate = date);
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Record Date',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Vital Signs Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Vital Signs',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Heart Rate
                    TextFormField(
                      controller: _heartRateController,
                      decoration: const InputDecoration(
                        labelText: 'Heart Rate (bpm)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.favorite, color: Colors.red),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final rate = int.tryParse(value);
                          if (rate == null || rate < 30 || rate > 200) {
                            return 'Enter a valid heart rate (30-200 bpm)';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Blood Pressure
                    TextFormField(
                      controller: _bloodPressureController,
                      decoration: const InputDecoration(
                        labelText: 'Blood Pressure (e.g., 120/80)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.monitor_heart,
                          color: Colors.blue,
                        ),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (!RegExp(r'^\d{2,3}\/\d{2,3}$').hasMatch(value)) {
                            return 'Enter in format: 120/80';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Weight
                    TextFormField(
                      controller: _weightController,
                      decoration: const InputDecoration(
                        labelText: 'Weight (kg)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.monitor_weight,
                          color: Colors.green,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final weight = double.tryParse(value);
                          if (weight == null || weight < 20 || weight > 300) {
                            return 'Enter a valid weight (20-300 kg)';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Temperature
                    TextFormField(
                      controller: _temperatureController,
                      decoration: const InputDecoration(
                        labelText: 'Temperature (°C)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.thermostat,
                          color: Colors.orange,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final temp = double.tryParse(value);
                          if (temp == null || temp < 30 || temp > 45) {
                            return 'Enter a valid temperature (30-45°C)';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Medical Information Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Medical Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Describe the reason for this record...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),

                    // Diagnosis
                    TextFormField(
                      controller: _diagnosisController,
                      decoration: const InputDecoration(
                        labelText: 'Diagnosis',
                        hintText: 'Enter diagnosis if available...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.medical_information,
                          color: AppColors.error,
                        ),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),

                    // Treatment
                    TextFormField(
                      controller: _treatmentController,
                      decoration: const InputDecoration(
                        labelText: 'Treatment',
                        hintText: 'Enter treatment plan...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.healing,
                          color: AppColors.success,
                        ),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Healthcare Provider Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Healthcare Provider',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Doctor Name
                    TextFormField(
                      controller: _doctorNameController,
                      decoration: const InputDecoration(
                        labelText: 'Doctor Name',
                        hintText: 'Enter doctor or healthcare provider name...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Facility Name
                    TextFormField(
                      controller: _facilityNameController,
                      decoration: const InputDecoration(
                        labelText: 'Healthcare Facility',
                        hintText: 'Enter hospital or clinic name...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.local_hospital),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Additional Notes Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Notes',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText:
                            'Enter any additional notes, symptoms, or observations...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.note),
                      ),
                      maxLines: 4,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: const BorderSide(color: AppColors.primary),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(fontSize: 16, color: AppColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _updateHealthRecord,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Text(
                              'Update Record',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
