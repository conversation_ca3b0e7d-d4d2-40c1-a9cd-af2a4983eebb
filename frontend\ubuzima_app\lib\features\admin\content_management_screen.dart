import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/widgets/loading_overlay.dart';
import '../../core/services/api_service.dart';
import '../../core/models/education_content.dart';

/// Admin Content Management Screen
class ContentManagementScreen extends ConsumerStatefulWidget {
  const ContentManagementScreen({super.key});

  @override
  ConsumerState<ContentManagementScreen> createState() => _ContentManagementScreenState();
}

class _ContentManagementScreenState extends ConsumerState<ContentManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  List<EducationContent> _articles = [];
  List<EducationContent> _videos = [];
  List<EducationContent> _filteredContent = [];
  
  bool _isLoading = false;
  String? _error;
  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await ApiService.instance.getEducationContent();
      
      if (response.success && response.data != null) {
        final contentData = response.data['content'] as List<dynamic>? ?? [];
        final allContent = contentData
            .map((json) => EducationContent.fromJson(json as Map<String, dynamic>))
            .toList();
        
        _articles = allContent.where((c) => c.type == 'article').toList();
        _videos = allContent.where((c) => c.type == 'video').toList();
        
        _filterContent();
      } else {
        _error = response.message ?? 'Failed to load content';
      }
    } catch (e) {
      _error = 'Error loading content: $e';
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _filterContent() {
    List<EducationContent> sourceList;
    switch (_tabController.index) {
      case 0:
        sourceList = [..._articles, ..._videos];
        break;
      case 1:
        sourceList = _articles;
        break;
      case 2:
        sourceList = _videos;
        break;
      default:
        sourceList = [];
    }

    String query = _searchController.text.toLowerCase();
    
    _filteredContent = sourceList.where((content) {
      final matchesSearch = content.title.toLowerCase().contains(query) ||
                           content.description.toLowerCase().contains(query);
      final matchesCategory = _selectedCategory == 'All' || 
                             content.category.toLowerCase() == _selectedCategory.toLowerCase();
      
      return matchesSearch && matchesCategory;
    }).toList();
    
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Content Management'),
        backgroundColor: AppColors.educationBlue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadContent,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          onTap: (_) => _filterContent(),
          tabs: const [
            Tab(text: 'All Content'),
            Tab(text: 'Articles'),
            Tab(text: 'Videos'),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Column(
          children: [
            _buildSearchAndFilter(),
            _buildContentStats(),
            Expanded(
              child: _error != null
                  ? _buildErrorState()
                  : _buildContentList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateContentDialog,
        backgroundColor: AppColors.educationBlue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search content...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.educationBlue),
              ),
            ),
            onChanged: (_) => _filterContent(),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('Category: ', style: TextStyle(fontWeight: FontWeight.bold)),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  items: ['All', 'Family Planning', 'Contraception', 'Pregnancy', 'Health', 'Nutrition']
                      .map((category) => DropdownMenuItem(value: category, child: Text(category)))
                      .toList(),
                  onChanged: (value) {
                    setState(() => _selectedCategory = value!);
                    _filterContent();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContentStats() {
    final totalContent = _articles.length + _videos.length;
    final publishedContent = _filteredContent.where((c) => c.isPublished).length;
    final draftContent = _filteredContent.where((c) => !c.isPublished).length;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          _buildStatCard('Total', totalContent, AppColors.primary),
          _buildStatCard('Articles', _articles.length, AppColors.educationBlue),
          _buildStatCard('Videos', _videos.length, AppColors.secondary),
          _buildStatCard('Published', publishedContent, AppColors.success),
          _buildStatCard('Drafts', draftContent, AppColors.warning),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, int count, Color color) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentList() {
    if (_filteredContent.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.article_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No content found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredContent.length,
      itemBuilder: (context, index) {
        final content = _filteredContent[index];
        return _buildContentCard(content);
      },
    );
  }

  Widget _buildContentCard(EducationContent content) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: content.type == 'article' 
              ? AppColors.educationBlue 
              : AppColors.secondary,
          child: Icon(
            content.type == 'article' ? Icons.article : Icons.play_arrow,
            color: Colors.white,
          ),
        ),
        title: Text(
          content.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              content.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.educationBlue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    content.category,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.educationBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: content.isPublished 
                        ? AppColors.success.withValues(alpha: 0.1) 
                        : AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    content.isPublished ? 'Published' : 'Draft',
                    style: TextStyle(
                      fontSize: 12,
                      color: content.isPublished ? AppColors.success : AppColors.warning,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleContentAction(content, value),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('View')),
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            PopupMenuItem(
              value: content.isPublished ? 'unpublish' : 'publish',
              child: Text(content.isPublished ? 'Unpublish' : 'Publish'),
            ),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('Error loading content', style: TextStyle(fontSize: 18, color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text(_error!, textAlign: TextAlign.center, style: TextStyle(color: Colors.grey[500])),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadContent,
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.educationBlue),
            child: const Text('Retry', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _handleContentAction(EducationContent content, String action) {
    switch (action) {
      case 'view':
        _viewContent(content);
        break;
      case 'edit':
        _editContent(content);
        break;
      case 'publish':
      case 'unpublish':
        _togglePublishStatus(content);
        break;
      case 'delete':
        _showDeleteConfirmation(content);
        break;
    }
  }

  void _viewContent(EducationContent content) {
    // TODO: Navigate to content view screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('View ${content.title} - Coming Soon')),
    );
  }

  void _editContent(EducationContent content) {
    // TODO: Navigate to content edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit ${content.title} - Coming Soon')),
    );
  }

  void _showCreateContentDialog() {
    // TODO: Show create content dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create content - Coming Soon')),
    );
  }

  Future<void> _togglePublishStatus(EducationContent content) async {
    // TODO: Implement toggle publish status API call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Toggle publish status for ${content.title} - Coming Soon')),
    );
  }

  void _showDeleteConfirmation(EducationContent content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Content'),
        content: Text('Are you sure you want to delete "${content.title}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteContent(content);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteContent(EducationContent content) async {
    // TODO: Implement delete content API call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Delete ${content.title} - Coming Soon')),
    );
  }
}
