import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/health_record.dart';
import '../models/menstrual_cycle.dart';
import '../models/medication.dart';
import '../models/appointment.dart';
import '../models/community_event.dart';

/// Health state for managing all health-related data
class HealthState {
  final List<HealthRecord> healthRecords;
  final List<MenstrualCycle> menstrualCycles;
  final List<Medication> medications;
  final List<Appointment> appointments;
  final bool isLoading;
  final String? error;

  HealthState({
    this.healthRecords = const [],
    this.menstrualCycles = const [],
    this.medications = const [],
    this.appointments = const [],
    this.isLoading = false,
    this.error,
  });

  HealthState copyWith({
    List<HealthRecord>? healthRecords,
    List<MenstrualCycle>? menstrualCycles,
    List<Medication>? medications,
    List<Appointment>? appointments,
    bool? isLoading,
    String? error,
  }) {
    return HealthState(
      healthRecords: healthRecords ?? this.healthRecords,
      menstrualCycles: menstrualCycles ?? this.menstrualCycles,
      medications: medications ?? this.medications,
      appointments: appointments ?? this.appointments,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Health provider for managing all health-related data and API calls
class HealthNotifier extends StateNotifier<HealthState> {
  HealthNotifier() : super(HealthState());

  final ApiService _apiService = ApiService.instance;

  /// Load all health data
  Future<void> loadAllHealthData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await Future.wait([
        loadHealthRecords(),
        loadMenstrualCycles(),
        loadMedications(),
        loadAppointments(),
      ]);

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load health data: $e',
      );
    }
  }

  /// Load health records from API
  Future<void> loadHealthRecords() async {
    try {
      final response = await _apiService.getHealthRecords();

      if (response.success && response.data != null) {
        final records =
            (response.data as List<dynamic>)
                .map((json) => HealthRecord.fromJson(json))
                .toList();

        state = state.copyWith(healthRecords: records);
      } else {
        throw Exception(response.message ?? 'Failed to load health records');
      }
    } catch (e) {
      state = state.copyWith(error: 'Error loading health records: $e');
      rethrow;
    }
  }

  /// Create health record
  Future<bool> createHealthRecord(HealthRecord record) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.createHealthRecord(record.toJson());

      if (response.success) {
        await loadHealthRecords(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create health record',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error creating health record: $e',
      );
      return false;
    }
  }

  /// Update health record
  Future<bool> updateHealthRecord(HealthRecord record) async {
    if (record.id == null) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.updateHealthRecord(
        record.id!,
        record.toJson(),
      );

      if (response.success) {
        await loadHealthRecords(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update health record',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error updating health record: $e',
      );
      return false;
    }
  }

  /// Delete health record
  Future<bool> deleteHealthRecord(int id) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.deleteHealthRecord(id);

      if (response.success) {
        await loadHealthRecords(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete health record',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error deleting health record: $e',
      );
      return false;
    }
  }

  /// Load menstrual cycles from API
  Future<void> loadMenstrualCycles() async {
    try {
      final response = await _apiService.getMenstrualCycles();

      if (response.success && response.data != null) {
        final cycles =
            (response.data as List<dynamic>)
                .map((json) => MenstrualCycle.fromJson(json))
                .toList();

        state = state.copyWith(menstrualCycles: cycles);
      } else {
        throw Exception(response.message ?? 'Failed to load menstrual cycles');
      }
    } catch (e) {
      state = state.copyWith(error: 'Error loading menstrual cycles: $e');
      rethrow;
    }
  }

  /// Create menstrual cycle
  Future<bool> createMenstrualCycle(MenstrualCycle cycle) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.createMenstrualCycle(cycle.toJson());

      if (response.success) {
        await loadMenstrualCycles(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create menstrual cycle',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error creating menstrual cycle: $e',
      );
      return false;
    }
  }

  /// Load medications from API
  Future<void> loadMedications() async {
    try {
      final response = await _apiService.getMedications();

      if (response.success && response.data != null) {
        final medications =
            (response.data as List<dynamic>)
                .map((json) => Medication.fromJson(json))
                .toList();

        state = state.copyWith(medications: medications);
      } else {
        throw Exception(response.message ?? 'Failed to load medications');
      }
    } catch (e) {
      state = state.copyWith(error: 'Error loading medications: $e');
      rethrow;
    }
  }

  /// Create medication
  Future<bool> createMedication(Medication medication) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.createMedication(medication.toJson());

      if (response.success) {
        await loadMedications(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create medication',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error creating medication: $e',
      );
      return false;
    }
  }

  /// Load appointments from API
  Future<void> loadAppointments() async {
    try {
      final response = await _apiService.getAppointments();

      if (response.success && response.data != null) {
        final appointments =
            (response.data as List<dynamic>)
                .map((json) => Appointment.fromJson(json))
                .toList();

        state = state.copyWith(appointments: appointments);
      } else {
        throw Exception(response.message ?? 'Failed to load appointments');
      }
    } catch (e) {
      state = state.copyWith(error: 'Error loading appointments: $e');
      rethrow;
    }
  }

  /// Create appointment
  Future<bool> createAppointment(Appointment appointment) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.createAppointment(
        appointment.toJson(),
      );

      if (response.success) {
        await loadAppointments(); // Refresh the list
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create appointment',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error creating appointment: $e',
      );
      return false;
    }
  }

  /// Get current menstrual cycle
  MenstrualCycle? get currentMenstrualCycle {
    if (state.menstrualCycles.isEmpty) return null;

    // Sort by start date and get the most recent
    final sortedCycles = List<MenstrualCycle>.from(state.menstrualCycles)
      ..sort((a, b) => b.startDate.compareTo(a.startDate));
    return sortedCycles.first;
  }

  /// Get next period prediction
  DateTime? get nextPeriodPrediction {
    final current = currentMenstrualCycle;
    if (current == null) return null;

    // Simple prediction: add average cycle length to start date
    return current.startDate.add(Duration(days: current.cycleLength ?? 28));
  }

  /// Get upcoming appointments
  List<Appointment> get upcomingAppointments {
    final now = DateTime.now();
    return state.appointments
        .where((appointment) => appointment.appointmentDate.isAfter(now))
        .toList()
      ..sort((a, b) => a.appointmentDate.compareTo(b.appointmentDate));
  }

  /// Get active medications
  List<Medication> get activeMedications {
    return state.medications
        .where((medication) => medication.isActive)
        .toList();
  }

  /// Clear all data
  void clearAllData() {
    state = HealthState();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // ==================== COMMUNITY EVENTS METHODS ====================

  /// Get community events
  Future<List<CommunityEvent>> getCommunityEvents({
    String? category,
    String? status,
  }) async {
    try {
      final response = await _apiService.getCommunityEvents(
        category: category,
        status: status,
      );

      if (response.success && response.data != null) {
        final eventsData = response.data['events'] as List<dynamic>? ?? [];
        return eventsData
            .map(
              (json) => CommunityEvent.fromJson(json as Map<String, dynamic>),
            )
            .toList();
      }
      return [];
    } catch (e) {
      state = state.copyWith(error: 'Failed to load community events: $e');
      return [];
    }
  }

  /// Get user's registered events
  Future<List<CommunityEvent>> getMyEvents() async {
    try {
      final response = await _apiService.getMyEvents();

      if (response.success && response.data != null) {
        final eventsData = response.data['events'] as List<dynamic>? ?? [];
        return eventsData
            .map(
              (json) => CommunityEvent.fromJson(json as Map<String, dynamic>),
            )
            .toList();
      }
      return [];
    } catch (e) {
      state = state.copyWith(error: 'Failed to load my events: $e');
      return [];
    }
  }

  /// Register for an event
  Future<bool> registerForEvent(int eventId) async {
    try {
      final response = await _apiService.registerForEvent(eventId);
      return response.success;
    } catch (e) {
      state = state.copyWith(error: 'Failed to register for event: $e');
      return false;
    }
  }

  /// Create a new community event
  Future<bool> createCommunityEvent(Map<String, dynamic> eventData) async {
    try {
      final response = await _apiService.createCommunityEvent(eventData);
      return response.success;
    } catch (e) {
      state = state.copyWith(error: 'Failed to create event: $e');
      return false;
    }
  }
}

/// Health provider
final healthProvider = StateNotifierProvider<HealthNotifier, HealthState>((
  ref,
) {
  return HealthNotifier();
});

/// Specific providers for different health data
final healthRecordsProvider = Provider<List<HealthRecord>>((ref) {
  final healthState = ref.watch(healthProvider);
  return healthState.healthRecords;
});

final menstrualCyclesProvider = Provider<List<MenstrualCycle>>((ref) {
  final healthState = ref.watch(healthProvider);
  return healthState.menstrualCycles;
});

final medicationsProvider = Provider<List<Medication>>((ref) {
  final healthState = ref.watch(healthProvider);
  return healthState.medications;
});

final appointmentsProvider = Provider<List<Appointment>>((ref) {
  final healthState = ref.watch(healthProvider);
  return healthState.appointments;
});

final upcomingAppointmentsProvider = Provider<List<Appointment>>((ref) {
  final healthNotifier = ref.watch(healthProvider.notifier);
  return healthNotifier.upcomingAppointments;
});

final activeMedicationsProvider = Provider<List<Medication>>((ref) {
  final healthNotifier = ref.watch(healthProvider.notifier);
  return healthNotifier.activeMedications;
});

final currentMenstrualCycleProvider = Provider<MenstrualCycle?>((ref) {
  final healthNotifier = ref.watch(healthProvider.notifier);
  return healthNotifier.currentMenstrualCycle;
});

final nextPeriodPredictionProvider = Provider<DateTime?>((ref) {
  final healthNotifier = ref.watch(healthProvider.notifier);
  return healthNotifier.nextPeriodPrediction;
});
